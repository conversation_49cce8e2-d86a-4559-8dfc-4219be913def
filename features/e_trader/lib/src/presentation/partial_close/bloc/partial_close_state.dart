part of 'partial_close_bloc.dart';

@freezed
sealed class PartialCloseState with _$PartialCloseState {
  const PartialCloseState._();

  factory PartialCloseState({
    @Default(CloseTradeProcessState.initial())
    CloseTradeProcessState closeTradeProcessState,
    @Default(PositionProcessState.loading()) PositionProcessState processState,
    PositionModel? position,
    @Default(0) int errorCounter,
    @Default(TradeComponentState<double, OrderSizeErrorCode>.loading())
    TradeComponentState<double, OrderSizeErrorCode> orderSizeState,
    @Default(0) double profit,
    String? currency,
    @Default(ClosePositionType.fullyClose) ClosePositionType closePositionType,
  }) = _PartialCloseState;

  bool isValid() => orderSizeState.isValid();
}

@freezed
sealed class CloseTradeProcessState with _$CloseTradeProcessState {
  const factory CloseTradeProcessState.initial() = CloseTradeInitialState;
  const factory CloseTradeProcessState.loading() = CloseTradeLoadingState;
  const factory CloseTradeProcessState.success() = CloseTradeSuccessState;
  const factory CloseTradeProcessState.error(int counter) =
      CloseTradeErrorState;
  const factory CloseTradeProcessState.marketClosed() =
      CloseTradeMarketClosedState;
}

@freezed
sealed class PositionProcessState with _$PositionProcessState {
  const factory PositionProcessState.loading() = PositionLoadingState;
  const factory PositionProcessState.success() = PositionSuccessState;
  const factory PositionProcessState.empty() = PositionEmptyState;
  const factory PositionProcessState.error() = PositionError;
}
