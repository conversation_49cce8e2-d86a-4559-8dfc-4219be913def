// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'partial_close_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PartialCloseEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartialCloseEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PartialCloseEvent()';
}


}

/// @nodoc
class $PartialCloseEventCopyWith<$Res>  {
$PartialCloseEventCopyWith(PartialCloseEvent _, $Res Function(PartialCloseEvent) __);
}


/// @nodoc


class _OnLoadPosition implements PartialCloseEvent {
  const _OnLoadPosition(this.positionId, this.symbol);
  

 final  int positionId;
 final  String symbol;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLoadPositionCopyWith<_OnLoadPosition> get copyWith => __$OnLoadPositionCopyWithImpl<_OnLoadPosition>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLoadPosition&&(identical(other.positionId, positionId) || other.positionId == positionId)&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,positionId,symbol);

@override
String toString() {
  return 'PartialCloseEvent.onLoadPosition(positionId: $positionId, symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$OnLoadPositionCopyWith<$Res> implements $PartialCloseEventCopyWith<$Res> {
  factory _$OnLoadPositionCopyWith(_OnLoadPosition value, $Res Function(_OnLoadPosition) _then) = __$OnLoadPositionCopyWithImpl;
@useResult
$Res call({
 int positionId, String symbol
});




}
/// @nodoc
class __$OnLoadPositionCopyWithImpl<$Res>
    implements _$OnLoadPositionCopyWith<$Res> {
  __$OnLoadPositionCopyWithImpl(this._self, this._then);

  final _OnLoadPosition _self;
  final $Res Function(_OnLoadPosition) _then;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? positionId = null,Object? symbol = null,}) {
  return _then(_OnLoadPosition(
null == positionId ? _self.positionId : positionId // ignore: cast_nullable_to_non_nullable
as int,null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _Resubscribe implements PartialCloseEvent {
  const _Resubscribe(this.positionId, this.symbol);
  

 final  int positionId;
 final  String symbol;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ResubscribeCopyWith<_Resubscribe> get copyWith => __$ResubscribeCopyWithImpl<_Resubscribe>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Resubscribe&&(identical(other.positionId, positionId) || other.positionId == positionId)&&(identical(other.symbol, symbol) || other.symbol == symbol));
}


@override
int get hashCode => Object.hash(runtimeType,positionId,symbol);

@override
String toString() {
  return 'PartialCloseEvent.resubscribe(positionId: $positionId, symbol: $symbol)';
}


}

/// @nodoc
abstract mixin class _$ResubscribeCopyWith<$Res> implements $PartialCloseEventCopyWith<$Res> {
  factory _$ResubscribeCopyWith(_Resubscribe value, $Res Function(_Resubscribe) _then) = __$ResubscribeCopyWithImpl;
@useResult
$Res call({
 int positionId, String symbol
});




}
/// @nodoc
class __$ResubscribeCopyWithImpl<$Res>
    implements _$ResubscribeCopyWith<$Res> {
  __$ResubscribeCopyWithImpl(this._self, this._then);

  final _Resubscribe _self;
  final $Res Function(_Resubscribe) _then;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? positionId = null,Object? symbol = null,}) {
  return _then(_Resubscribe(
null == positionId ? _self.positionId : positionId // ignore: cast_nullable_to_non_nullable
as int,null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnCloseTrade implements PartialCloseEvent {
  const _OnCloseTrade(this.symbol, {this.volume});
  

 final  String symbol;
 final  int? volume;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnCloseTradeCopyWith<_OnCloseTrade> get copyWith => __$OnCloseTradeCopyWithImpl<_OnCloseTrade>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnCloseTrade&&(identical(other.symbol, symbol) || other.symbol == symbol)&&(identical(other.volume, volume) || other.volume == volume));
}


@override
int get hashCode => Object.hash(runtimeType,symbol,volume);

@override
String toString() {
  return 'PartialCloseEvent.onCloseTrade(symbol: $symbol, volume: $volume)';
}


}

/// @nodoc
abstract mixin class _$OnCloseTradeCopyWith<$Res> implements $PartialCloseEventCopyWith<$Res> {
  factory _$OnCloseTradeCopyWith(_OnCloseTrade value, $Res Function(_OnCloseTrade) _then) = __$OnCloseTradeCopyWithImpl;
@useResult
$Res call({
 String symbol, int? volume
});




}
/// @nodoc
class __$OnCloseTradeCopyWithImpl<$Res>
    implements _$OnCloseTradeCopyWith<$Res> {
  __$OnCloseTradeCopyWithImpl(this._self, this._then);

  final _OnCloseTrade _self;
  final $Res Function(_OnCloseTrade) _then;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? symbol = null,Object? volume = freezed,}) {
  return _then(_OnCloseTrade(
null == symbol ? _self.symbol : symbol // ignore: cast_nullable_to_non_nullable
as String,volume: freezed == volume ? _self.volume : volume // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class _PartialCloseLotSizeChanged implements PartialCloseEvent {
  const _PartialCloseLotSizeChanged(this.state);
  

 final  TradeComponentState<double, OrderSizeErrorCode> state;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartialCloseLotSizeChangedCopyWith<_PartialCloseLotSizeChanged> get copyWith => __$PartialCloseLotSizeChangedCopyWithImpl<_PartialCloseLotSizeChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartialCloseLotSizeChanged&&(identical(other.state, state) || other.state == state));
}


@override
int get hashCode => Object.hash(runtimeType,state);

@override
String toString() {
  return 'PartialCloseEvent.orderSizeChanged(state: $state)';
}


}

/// @nodoc
abstract mixin class _$PartialCloseLotSizeChangedCopyWith<$Res> implements $PartialCloseEventCopyWith<$Res> {
  factory _$PartialCloseLotSizeChangedCopyWith(_PartialCloseLotSizeChanged value, $Res Function(_PartialCloseLotSizeChanged) _then) = __$PartialCloseLotSizeChangedCopyWithImpl;
@useResult
$Res call({
 TradeComponentState<double, OrderSizeErrorCode> state
});


$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get state;

}
/// @nodoc
class __$PartialCloseLotSizeChangedCopyWithImpl<$Res>
    implements _$PartialCloseLotSizeChangedCopyWith<$Res> {
  __$PartialCloseLotSizeChangedCopyWithImpl(this._self, this._then);

  final _PartialCloseLotSizeChanged _self;
  final $Res Function(_PartialCloseLotSizeChanged) _then;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? state = null,}) {
  return _then(_PartialCloseLotSizeChanged(
null == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,
  ));
}

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get state {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.state, (value) {
    return _then(_self.copyWith(state: value));
  });
}
}

/// @nodoc


class _ClosePositionTypeChanged implements PartialCloseEvent {
  const _ClosePositionTypeChanged(this.closePositionType);
  

 final  ClosePositionType closePositionType;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ClosePositionTypeChangedCopyWith<_ClosePositionTypeChanged> get copyWith => __$ClosePositionTypeChangedCopyWithImpl<_ClosePositionTypeChanged>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ClosePositionTypeChanged&&(identical(other.closePositionType, closePositionType) || other.closePositionType == closePositionType));
}


@override
int get hashCode => Object.hash(runtimeType,closePositionType);

@override
String toString() {
  return 'PartialCloseEvent.closePositionTypeChanged(closePositionType: $closePositionType)';
}


}

/// @nodoc
abstract mixin class _$ClosePositionTypeChangedCopyWith<$Res> implements $PartialCloseEventCopyWith<$Res> {
  factory _$ClosePositionTypeChangedCopyWith(_ClosePositionTypeChanged value, $Res Function(_ClosePositionTypeChanged) _then) = __$ClosePositionTypeChangedCopyWithImpl;
@useResult
$Res call({
 ClosePositionType closePositionType
});




}
/// @nodoc
class __$ClosePositionTypeChangedCopyWithImpl<$Res>
    implements _$ClosePositionTypeChangedCopyWith<$Res> {
  __$ClosePositionTypeChangedCopyWithImpl(this._self, this._then);

  final _ClosePositionTypeChanged _self;
  final $Res Function(_ClosePositionTypeChanged) _then;

/// Create a copy of PartialCloseEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? closePositionType = null,}) {
  return _then(_ClosePositionTypeChanged(
null == closePositionType ? _self.closePositionType : closePositionType // ignore: cast_nullable_to_non_nullable
as ClosePositionType,
  ));
}


}

/// @nodoc
mixin _$PartialCloseState {

 CloseTradeProcessState get closeTradeProcessState; PositionProcessState get processState; PositionModel? get position; int get errorCounter; TradeComponentState<double, OrderSizeErrorCode> get orderSizeState; double get profit; String? get currency; ClosePositionType get closePositionType;
/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PartialCloseStateCopyWith<PartialCloseState> get copyWith => _$PartialCloseStateCopyWithImpl<PartialCloseState>(this as PartialCloseState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PartialCloseState&&(identical(other.closeTradeProcessState, closeTradeProcessState) || other.closeTradeProcessState == closeTradeProcessState)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.position, position) || other.position == position)&&(identical(other.errorCounter, errorCounter) || other.errorCounter == errorCounter)&&(identical(other.orderSizeState, orderSizeState) || other.orderSizeState == orderSizeState)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.closePositionType, closePositionType) || other.closePositionType == closePositionType));
}


@override
int get hashCode => Object.hash(runtimeType,closeTradeProcessState,processState,position,errorCounter,orderSizeState,profit,currency,closePositionType);

@override
String toString() {
  return 'PartialCloseState(closeTradeProcessState: $closeTradeProcessState, processState: $processState, position: $position, errorCounter: $errorCounter, orderSizeState: $orderSizeState, profit: $profit, currency: $currency, closePositionType: $closePositionType)';
}


}

/// @nodoc
abstract mixin class $PartialCloseStateCopyWith<$Res>  {
  factory $PartialCloseStateCopyWith(PartialCloseState value, $Res Function(PartialCloseState) _then) = _$PartialCloseStateCopyWithImpl;
@useResult
$Res call({
 CloseTradeProcessState closeTradeProcessState, PositionProcessState processState, PositionModel? position, int errorCounter, TradeComponentState<double, OrderSizeErrorCode> orderSizeState, double profit, String? currency, ClosePositionType closePositionType
});


$CloseTradeProcessStateCopyWith<$Res> get closeTradeProcessState;$PositionProcessStateCopyWith<$Res> get processState;$PositionModelCopyWith<$Res>? get position;$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState;

}
/// @nodoc
class _$PartialCloseStateCopyWithImpl<$Res>
    implements $PartialCloseStateCopyWith<$Res> {
  _$PartialCloseStateCopyWithImpl(this._self, this._then);

  final PartialCloseState _self;
  final $Res Function(PartialCloseState) _then;

/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? closeTradeProcessState = null,Object? processState = null,Object? position = freezed,Object? errorCounter = null,Object? orderSizeState = null,Object? profit = null,Object? currency = freezed,Object? closePositionType = null,}) {
  return _then(_self.copyWith(
closeTradeProcessState: null == closeTradeProcessState ? _self.closeTradeProcessState : closeTradeProcessState // ignore: cast_nullable_to_non_nullable
as CloseTradeProcessState,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as PositionProcessState,position: freezed == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as PositionModel?,errorCounter: null == errorCounter ? _self.errorCounter : errorCounter // ignore: cast_nullable_to_non_nullable
as int,orderSizeState: null == orderSizeState ? _self.orderSizeState : orderSizeState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,profit: null == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,closePositionType: null == closePositionType ? _self.closePositionType : closePositionType // ignore: cast_nullable_to_non_nullable
as ClosePositionType,
  ));
}
/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CloseTradeProcessStateCopyWith<$Res> get closeTradeProcessState {
  
  return $CloseTradeProcessStateCopyWith<$Res>(_self.closeTradeProcessState, (value) {
    return _then(_self.copyWith(closeTradeProcessState: value));
  });
}/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionProcessStateCopyWith<$Res> get processState {
  
  return $PositionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionModelCopyWith<$Res>? get position {
    if (_self.position == null) {
    return null;
  }

  return $PositionModelCopyWith<$Res>(_self.position!, (value) {
    return _then(_self.copyWith(position: value));
  });
}/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.orderSizeState, (value) {
    return _then(_self.copyWith(orderSizeState: value));
  });
}
}


/// @nodoc


class _PartialCloseState extends PartialCloseState {
   _PartialCloseState({this.closeTradeProcessState = const CloseTradeProcessState.initial(), this.processState = const PositionProcessState.loading(), this.position, this.errorCounter = 0, this.orderSizeState = const TradeComponentState<double, OrderSizeErrorCode>.loading(), this.profit = 0, this.currency, this.closePositionType = ClosePositionType.fullyClose}): super._();
  

@override@JsonKey() final  CloseTradeProcessState closeTradeProcessState;
@override@JsonKey() final  PositionProcessState processState;
@override final  PositionModel? position;
@override@JsonKey() final  int errorCounter;
@override@JsonKey() final  TradeComponentState<double, OrderSizeErrorCode> orderSizeState;
@override@JsonKey() final  double profit;
@override final  String? currency;
@override@JsonKey() final  ClosePositionType closePositionType;

/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PartialCloseStateCopyWith<_PartialCloseState> get copyWith => __$PartialCloseStateCopyWithImpl<_PartialCloseState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PartialCloseState&&(identical(other.closeTradeProcessState, closeTradeProcessState) || other.closeTradeProcessState == closeTradeProcessState)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.position, position) || other.position == position)&&(identical(other.errorCounter, errorCounter) || other.errorCounter == errorCounter)&&(identical(other.orderSizeState, orderSizeState) || other.orderSizeState == orderSizeState)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.closePositionType, closePositionType) || other.closePositionType == closePositionType));
}


@override
int get hashCode => Object.hash(runtimeType,closeTradeProcessState,processState,position,errorCounter,orderSizeState,profit,currency,closePositionType);

@override
String toString() {
  return 'PartialCloseState(closeTradeProcessState: $closeTradeProcessState, processState: $processState, position: $position, errorCounter: $errorCounter, orderSizeState: $orderSizeState, profit: $profit, currency: $currency, closePositionType: $closePositionType)';
}


}

/// @nodoc
abstract mixin class _$PartialCloseStateCopyWith<$Res> implements $PartialCloseStateCopyWith<$Res> {
  factory _$PartialCloseStateCopyWith(_PartialCloseState value, $Res Function(_PartialCloseState) _then) = __$PartialCloseStateCopyWithImpl;
@override @useResult
$Res call({
 CloseTradeProcessState closeTradeProcessState, PositionProcessState processState, PositionModel? position, int errorCounter, TradeComponentState<double, OrderSizeErrorCode> orderSizeState, double profit, String? currency, ClosePositionType closePositionType
});


@override $CloseTradeProcessStateCopyWith<$Res> get closeTradeProcessState;@override $PositionProcessStateCopyWith<$Res> get processState;@override $PositionModelCopyWith<$Res>? get position;@override $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState;

}
/// @nodoc
class __$PartialCloseStateCopyWithImpl<$Res>
    implements _$PartialCloseStateCopyWith<$Res> {
  __$PartialCloseStateCopyWithImpl(this._self, this._then);

  final _PartialCloseState _self;
  final $Res Function(_PartialCloseState) _then;

/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? closeTradeProcessState = null,Object? processState = null,Object? position = freezed,Object? errorCounter = null,Object? orderSizeState = null,Object? profit = null,Object? currency = freezed,Object? closePositionType = null,}) {
  return _then(_PartialCloseState(
closeTradeProcessState: null == closeTradeProcessState ? _self.closeTradeProcessState : closeTradeProcessState // ignore: cast_nullable_to_non_nullable
as CloseTradeProcessState,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as PositionProcessState,position: freezed == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as PositionModel?,errorCounter: null == errorCounter ? _self.errorCounter : errorCounter // ignore: cast_nullable_to_non_nullable
as int,orderSizeState: null == orderSizeState ? _self.orderSizeState : orderSizeState // ignore: cast_nullable_to_non_nullable
as TradeComponentState<double, OrderSizeErrorCode>,profit: null == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double,currency: freezed == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String?,closePositionType: null == closePositionType ? _self.closePositionType : closePositionType // ignore: cast_nullable_to_non_nullable
as ClosePositionType,
  ));
}

/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CloseTradeProcessStateCopyWith<$Res> get closeTradeProcessState {
  
  return $CloseTradeProcessStateCopyWith<$Res>(_self.closeTradeProcessState, (value) {
    return _then(_self.copyWith(closeTradeProcessState: value));
  });
}/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionProcessStateCopyWith<$Res> get processState {
  
  return $PositionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PositionModelCopyWith<$Res>? get position {
    if (_self.position == null) {
    return null;
  }

  return $PositionModelCopyWith<$Res>(_self.position!, (value) {
    return _then(_self.copyWith(position: value));
  });
}/// Create a copy of PartialCloseState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res> get orderSizeState {
  
  return $TradeComponentStateCopyWith<double, OrderSizeErrorCode, $Res>(_self.orderSizeState, (value) {
    return _then(_self.copyWith(orderSizeState: value));
  });
}
}

/// @nodoc
mixin _$CloseTradeProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseTradeProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CloseTradeProcessState()';
}


}

/// @nodoc
class $CloseTradeProcessStateCopyWith<$Res>  {
$CloseTradeProcessStateCopyWith(CloseTradeProcessState _, $Res Function(CloseTradeProcessState) __);
}


/// @nodoc


class CloseTradeInitialState implements CloseTradeProcessState {
  const CloseTradeInitialState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseTradeInitialState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CloseTradeProcessState.initial()';
}


}




/// @nodoc


class CloseTradeLoadingState implements CloseTradeProcessState {
  const CloseTradeLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseTradeLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CloseTradeProcessState.loading()';
}


}




/// @nodoc


class CloseTradeSuccessState implements CloseTradeProcessState {
  const CloseTradeSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseTradeSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CloseTradeProcessState.success()';
}


}




/// @nodoc


class CloseTradeErrorState implements CloseTradeProcessState {
  const CloseTradeErrorState(this.counter);
  

 final  int counter;

/// Create a copy of CloseTradeProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CloseTradeErrorStateCopyWith<CloseTradeErrorState> get copyWith => _$CloseTradeErrorStateCopyWithImpl<CloseTradeErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseTradeErrorState&&(identical(other.counter, counter) || other.counter == counter));
}


@override
int get hashCode => Object.hash(runtimeType,counter);

@override
String toString() {
  return 'CloseTradeProcessState.error(counter: $counter)';
}


}

/// @nodoc
abstract mixin class $CloseTradeErrorStateCopyWith<$Res> implements $CloseTradeProcessStateCopyWith<$Res> {
  factory $CloseTradeErrorStateCopyWith(CloseTradeErrorState value, $Res Function(CloseTradeErrorState) _then) = _$CloseTradeErrorStateCopyWithImpl;
@useResult
$Res call({
 int counter
});




}
/// @nodoc
class _$CloseTradeErrorStateCopyWithImpl<$Res>
    implements $CloseTradeErrorStateCopyWith<$Res> {
  _$CloseTradeErrorStateCopyWithImpl(this._self, this._then);

  final CloseTradeErrorState _self;
  final $Res Function(CloseTradeErrorState) _then;

/// Create a copy of CloseTradeProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? counter = null,}) {
  return _then(CloseTradeErrorState(
null == counter ? _self.counter : counter // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class CloseTradeMarketClosedState implements CloseTradeProcessState {
  const CloseTradeMarketClosedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CloseTradeMarketClosedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CloseTradeProcessState.marketClosed()';
}


}




/// @nodoc
mixin _$PositionProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionProcessState()';
}


}

/// @nodoc
class $PositionProcessStateCopyWith<$Res>  {
$PositionProcessStateCopyWith(PositionProcessState _, $Res Function(PositionProcessState) __);
}


/// @nodoc


class PositionLoadingState implements PositionProcessState {
  const PositionLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionProcessState.loading()';
}


}




/// @nodoc


class PositionSuccessState implements PositionProcessState {
  const PositionSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionProcessState.success()';
}


}




/// @nodoc


class PositionEmptyState implements PositionProcessState {
  const PositionEmptyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionEmptyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionProcessState.empty()';
}


}




/// @nodoc


class PositionError implements PositionProcessState {
  const PositionError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PositionError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'PositionProcessState.error()';
}


}




// dart format on
