part of 'partial_close.dart';

class _PartialCloseContent extends StatefulWidget {
  const _PartialCloseContent({
    required this.positionId,
    required this.platformName,
    required this.onSuccess,
  });

  final int positionId;
  final String platformName;
  final void Function({
    required double lots,
    required String productIconUrl,
    required String productName,
    required double profit,
    required TradeType? tradeType,
    String? titleMessage,
  })
  onSuccess;

  @override
  State<_PartialCloseContent> createState() => _PartialCloseContentState();
}

class _PartialCloseContentState extends State<_PartialCloseContent>
    with RouteAwareAppLifecycleMixin {
  bool _hasSubscribed = true;
  DuploToast? toast;
  double? _originalPositionSize;

  @override
  void onAppForeground() {
    _subscribe();
  }

  @override
  void onAppBackground() {
    _unsubscribe();
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    super.onRoutePopped(route);
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    super.onRoutePushed(route);
    _unsubscribe();
  }

  void _subscribe() {
    if (!_hasSubscribed && mounted) {
      // Resubscribe to positions
      context.read<PartialCloseBloc>().add(
        PartialCloseEvent.resubscribe(widget.positionId, widget.platformName),
      );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdatePositionsUseCase>().call(
          eventType: TradingSocketEvent.positions.unsubscribe,
          symbolName: widget.platformName,
          positionId: widget.positionId,
        );
      } catch (e) {
        diContainer<LoggerBase>().logError(e, stackTrace: StackTrace.current);
      }
      _hasSubscribed = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final l10n = EquitiLocalization.of(context);
    final textStyles = context.duploTextStyles;

    return Container(
      color: theme.background.bgSecondary,
      child: BlocConsumer<PartialCloseBloc, PartialCloseState>(
        listenWhen: (previous, current) {
          if (previous.closeTradeProcessState is! CloseTradeLoadingState &&
              current.closeTradeProcessState is CloseTradeLoadingState &&
              current.position != null) {
            _originalPositionSize = current.position!.lotSize;
          }
          return current.closeTradeProcessState !=
              previous.closeTradeProcessState;
        },
        listener: (listenerContext, state) {
          if (state.closeTradeProcessState ==
              CloseTradeProcessState.success()) {
            final isFullyClosed =
                state.orderSizeState.value >=
                (_originalPositionSize ?? state.position!.lotSize);
            widget.onSuccess(
              lots: (state.orderSizeState.value),
              productIconUrl: state.position!.productLogoUrl,
              productName: state.position!.tickerName,
              profit: state.profit,
              tradeType: state.position!.positionType,
              titleMessage:
                  isFullyClosed
                      ? l10n.trader_tradeClosed
                      : l10n.trader_tradePartiallyClosed,
            );
            if (isFullyClosed) {
              Navigator.pop(context);
            }
            Navigator.pop(context);
          }
          if (state.closeTradeProcessState case CloseTradeErrorState(
            :final counter,
          )) {
            toast?.hidesToastMessage();
            toast = DuploToast();
            toast!.showToastMessage(
              context: context,
              autoCloseDuration: Duration.zero,
              widget: DuploTheme(
                data: DuploTheme.of(context),
                child: DuploTextStyles(
                  locale: Localizations.localeOf(context),
                  child: DuploToastMessage(
                    titleMessage:
                        counter == 1
                            ? l10n.trader_tradeNotClosed
                            : l10n.trader_contactSupportTeam,
                    descriptionMessage:
                        counter == 1
                            ? l10n.trader_placeholderText
                            : l10n.trader_contactSupportDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () {
                      toast?.hidesToastMessage();
                    },
                    primaryButtonTitle:
                        counter == 1 ? null : l10n.trader_raiseSupportTicket,
                    onTap: () {
                      toast?.hidesToastMessage(); // TODO: Raise support ticket
                    },
                  ),
                ),
              ),
            );
          } else if (state.closeTradeProcessState
              is CloseTradeMarketClosedState) {
            toast?.hidesToastMessage();
            toast = DuploToast();
            toast?.showToastMessage(
              context: context,
              widget: DuploTheme(
                data: DuploTheme.of(context),
                child: DuploTextStyles(
                  locale: Localizations.localeOf(context),
                  child: DuploToastMessage(
                    titleMessage: l10n.trader_marketIsClosed,
                    descriptionMessage:
                        l10n.trader_closeTrade_marketIsClosedDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast?.hidesToastMessage(),
                  ),
                ),
              ),
            );
          }
        },
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          return switch (state.processState) {
            PositionLoadingState() => Center(
              child: DuploShimmerListItem(height: 97),
            ),
            PositionSuccessState() => () {
              final position = state.position!;
              final locale = Localizations.localeOf(context).toString();
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: ListView(
                      children: [
                        TradeTile(
                          lots: position.lotSize,
                          profit: position.profit!,
                          tradeType: position.positionType,
                          tpValue: position.takeProfit,
                          slValue: position.stopLoss,
                          currentPrice: position.currentPrice,
                          priceChange:
                              position.positionType == TradeType.buy
                                  ? position.buyPercentage
                                  : position.sellPercentage,
                          productIcon: position.productLogoUrl,
                          productName: position.tickerName,
                          digits: position.digits,
                          currency: state.currency,
                          openPrice: position.openPrice,
                        ),
                        Divider(color: theme.border.borderSecondary, height: 0),
                        Container(
                          decoration: BoxDecoration(
                            color: theme.background.bgPrimary,
                            border: BorderDirectional(
                              start: BorderSide(
                                color:
                                    position.positionType == TradeType.buy
                                        ? theme.foreground.fgSuccessPrimary
                                        : theme.border.borderError,
                                width: 4.0,
                              ),
                              end: BorderSide.none,
                            ),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: DuploKeyValueDisplay(
                              hideLastDivider: false,
                              addBorder: false,
                              contentSpacing: 2,
                              keyTextStyle: textStyles.textXs,
                              valueTextStyle: textStyles.textXs,
                              keyValuePairs: [
                                KeyValuePair(
                                  label: l10n.trader_currentPriceTitle,
                                  value: EquitiFormatter.formatTradePrice(
                                    value: position.currentPrice,
                                    digits: position.digits,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                                ),
                                KeyValuePair(
                                  label: l10n.trader_openPriceTitle,
                                  value: EquitiFormatter.formatTradePrice(
                                    value: position.openPrice,
                                    digits: position.digits,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.background.bgPrimary,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.border.borderSecondary,
                            ),
                          ),
                          padding: const EdgeInsets.all(16),
                          child: InputOrderSizeWidget(
                            args: (
                              minLot: position.minLot,
                              maxLot: position.lotSize,
                              initialOrderSize: position.lotSize,
                              isDisabled: false,
                              lotsSteps: position.minLot,
                            ),
                            tradeType: state.position?.positionType,
                            onOrderSizeChanged:
                                (orderSizeState) =>
                                    builderContext.read<PartialCloseBloc>().add(
                                      PartialCloseEvent.orderSizeChanged(
                                        orderSizeState,
                                      ),
                                    ),
                            showBorder: false,
                            footer: Row(
                              children: [
                                DuploText(
                                  text: '${l10n.trader_profit}: ',
                                  style: DuploTextStyles.of(context).textXs,
                                  color: theme.text.textPrimary.withValues(
                                    alpha: .7,
                                  ),
                                ),
                                Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: DuploText(
                                    text:
                                        (state.profit > 0 ? "+" : '') +
                                        '${EquitiFormatter.formatTradeProfitOrLoss(value: state.profit, locale: locale)} ${state.currency ?? "USD"}',
                                    style: DuploTextStyles.of(context).textXs,
                                    color:
                                        state.profit < 0
                                            ? theme.text.textErrorPrimary
                                            : theme.text.textSuccessPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }(),
            _ => EmptyOrErrorStateComponent.defaultError(context, () {
              builderContext.read<PartialCloseBloc>().add(
                PartialCloseEvent.onLoadPosition(
                  widget.positionId,
                  widget.platformName,
                ),
              );
              _hasSubscribed = true;
            }),
          };
        },
      ),
    );
  }
}
