name: equiti_platform
description: "A new Flutter project."
publish_to: "none" # Remove this line if you wish to publish to pub.dev
version: 1.0.3+1

environment:
  sdk: 3.8.1
  flutter: 3.32.6

dependencies:
  equiti_auth:
    path: ../../core/equiti_auth
  flutter:
    sdk: flutter
  flutter_native_splash: 2.4.4
  monitoring:
    path: ../../utilities/monitoring
  api_client:
    path: ../../utilities/api_client
  e_trader:
    path: ../../features/e_trader
  payment:
   path: ../../features/payments 
  hub:
    path: ../../features/hub
  preferences:
    path: ../../utilities/preferences
  duplo:
    path: ../../core/duplo
  domain:
    path: ../../core/domain
  firebase:
    path: ../../core/firebase
  vector_graphics: 1.1.19
  dio: 5.8.0+1
  socket_client:
    path: ../../utilities/socket_client
  prelude:
    path: ../../utilities/prelude
  custom_action_keyboard:
    path: ../../utilities/custom_action_keyboard
  login:
    path: ../../core/login
  onboarding:
    path: ../../core/onboarding 
  locale_manager:
    path: ../../utilities/locale_manager
  theme_manager:
    path: ../../utilities/theme_manager
  equiti_localization:
    path: ../../core/equiti_localization
  user_account:
    path: ../../core/user_account
  customer_support_chat:
    path: ../../utilities/customer_support_chat
  broker_settings:
    path: ../../core/broker_settings
  equiti_analytics:
    path: ../../core/equiti_analytics
  equiti_identity:
    path: ../../core/equiti_identity
  equiti_secure_storage:
    path: ../../utilities/equiti_secure_storage

  equiti_router:
    path: ../../utilities/equiti_router
  network_logging:
    path: ../../utilities/network_logging
  clock: 1.1.2
  app_links: ^6.3.2
  
  get_it: 8.0.3
  injectable: 2.5.0
  flutter_svg: 2.0.10+1
  feature_flags:
    path: ../../core/feature_flags

dev_dependencies:
  flutter_launcher_icons: 0.14.2
  dependency_validator: 5.0.2
  flutter_gen_runner: 5.11.0
  equiti_lint:
    path: ../../utilities/equiti_lint
  injectable_generator: 2.7.0
  build_runner: 2.5.4
  dart_code_metrics_presets: 2.22.0
  build_verify: 3.1.1
  test: 1.25.15
  vector_graphics_compiler: 1.1.17

dependency_overrides:
  analyzer: ^6.3.0
  dart_style: ^3.0.1

flutter_launcher_icons:
  ios: true
  android: true
  min_sdk_android: 28
  image_path_ios: "assets/common/app-icon.png"
  image_path_android: "assets/android/app-icon-android.png"
  adaptive_icon_background: "assets/android/app-icon-background.png"
  adaptive_icon_foreground: "assets/android/app-icon-foreground.png"
  image_path_ios_dark_transparent: "assets/common/app-icon-transparent.png"
  image_path_ios_tinted_grayscale: "assets/common/app-icon-transparent.png"
  desaturate_tinted_to_grayscale_ios: true
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - path: assets/svg/
      transformers:
        - package: vector_graphics_compiler
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_gen:
  output: lib/src/assets/
  assets:
  integrations:
    flutter_svg: true
